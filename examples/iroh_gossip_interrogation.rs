use futures_lite::StreamExt as _;
use iroh::{protocol::Router, Endpoint};
use iroh_gossip::{net::Gossip, ALPN};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // create an iroh endpoint that includes the standard discovery mechanisms
    // we've built at number0
    let endpoint = Endpoint::builder().discovery_n0().bind().await?;

    // build gossip protocol
    let gossip = Gossip::builder().spawn(endpoint.clone());

    

    // setup router
    let router = Router::builder(endpoint.clone())
        .accept(ALPN, gossip.clone())
        .spawn();

    // Example: How to track known peers through events
    // Note: This is a conceptual example - you would need actual bootstrap peers
    // and proper topic setup to see real peer events

    println!("Gossip protocol initialized!");
    println!("To track peers, you would:");
    println!("1. Subscribe to a topic with bootstrap peers");
    println!("2. Listen to Event::NeighborUp and Event::NeighborDown events");
    println!("3. Maintain a HashSet or similar collection to track connected peers");

    // Example code structure (commented out as it needs real bootstrap peers):
    
    use iroh_gossip::api::Event;
    use std::collections::HashSet;

    let topic_id = iroh_gossip::proto::TopicId::from([1u8; 32]);
    let bootstrap_peers: Vec<iroh::NodeId> = vec![]; // Add real peer NodeIds
    let mut topic = gossip.subscribe(topic_id, bootstrap_peers).await?;

    let (sender,mut receiver) = topic.split();
    let mut connected_peers: HashSet<iroh::NodeId> = HashSet::new();

    let a = receiver.neighbors().collect::<HashSet<_>>();
    while let Some(event_result) = receiver.next().await {
        match event_result? {
            Event::NeighborUp(peer_id) => {
                connected_peers.insert(peer_id);
                println!("Peer connected: {}. Total peers: {}", peer_id, connected_peers.len());
            }
            Event::NeighborDown(peer_id) => {
                connected_peers.remove(&peer_id);
                println!("Peer disconnected: {}. Total peers: {}", peer_id, connected_peers.len());
            }
            Event::Received(message) => {
                println!("Message from {}", message.delivered_from);
            }
            Event::Lagged => {
                println!("Lagged behind in processing events");
            }
        }
    }
    

    // do fun stuff with the gossip protocol
    router.shutdown().await?;
    Ok(())
}