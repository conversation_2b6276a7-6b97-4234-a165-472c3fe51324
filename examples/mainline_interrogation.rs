
use futures::future::join_all;
use mainline::{Dht, Id, <PERSON><PERSON><PERSON><PERSON>, Signing<PERSON><PERSON>};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    let signing_key = SigningKey::generate(&mut rand::thread_rng());
    let dht = Dht::client()?;
    let id = write_record(dht.clone(), signing_key.clone(), b"hello world".to_vec(),Some(b"1234"))?;

    let mut futures = vec![];
    for i in 0..10 {
        futures.push(tokio::spawn({
            let dht = dht.clone();
            let signing_key = signing_key.clone();
            async move {
                tokio::time::sleep(std::time::Duration::from_millis(rand::random::<u64>() % 1000)).await;
                let res = write_record(
                    dht.clone(),
                    signing_key,
                    format!("{}", i).as_bytes().to_vec(),
                    Some(b"123"),
                );
                println!("res-{} is error={}", i, res.is_err());
            }
        }));
    }

    join_all(futures).await;

    let records = dht.get_mutable(signing_key.verifying_key().as_bytes(), Some(b"123"),None).collect::<Vec<_>>();
    let ih_records = dht.get_immutable(id);
    println!("ih_records: {:?}", ih_records);

    println!("records: {:?}", records);

    // ctrl-c to exit
    //tokio::signal::ctrl_c().await?;

    Ok(())
}

fn write_record(dht: Dht, signing_key: SigningKey, record: Vec<u8>,salt: Option<&[u8]>) -> anyhow::Result<Id> {
    let item = if let Some(mut_item)  = dht.get_mutable_most_recent(signing_key.verifying_key().as_bytes(), None) {
        MutableItem::new(signing_key, &record, mut_item.seq() + 1, salt)
    } else {
        MutableItem::new(signing_key, &record, 0, salt)
    };
    
    //let item = MutableItem::new(signing_key, &record, 0, Some(b"123"));

    let id = dht.put_mutable(item.clone(), Some(item.seq()))?;


    Ok(id)
}
