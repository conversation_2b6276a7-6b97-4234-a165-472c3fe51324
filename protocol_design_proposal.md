# Distributed Topic Tracker

## 1. Introduction

Protocol proposal for a distributed topic tracker that uses pkarr as the information layer and native ed25519 key derivation for topic addressing. Since all users derive the addresses from the same "seed", native ed25519 key derivation allows all users who know the topic to obtain the secret key for writing the topic record. That is where the **Stewards** come in. Stewards are peers in the same gossip topic that have "won" an election (see **Steward Election** below).

Every minute all nodes listed in last minute’s record will calculate their own election result from:

```bash
hash(topic + node_id + unix_minute) as u512 sorted in descending order
```

If your hash is in the top five nodes, you are a Steward for this minute. If no nodes are found in the last record that are eligible (this includes the publisher node_id from the last record), we must be in a low connectivity regimen: install yourself as steward[0] and write a record (this full record will only contain your node_id as actual info to connect other peers to the network).

---

## 2. Goals & Assumptions

**Goals**  
- Liveness: At least one valid record per minute  
- Safety: No two conflicting records survive past steward corrections  
- Scalability: Works from 2 to 1_000_000 peers

**Assumptions**  
- Clocks are loosely synchronized (±5 s)  
- DHT convergence ≈ 10 s  
- Ed25519 keys derived deterministically from topic seed  
- Gossip layer delivers `gossip.neighbors()` or if we only get the five active connections:

```rust
let mut all_peers = Vec::new();
while let Some(event_result) = receiver.next().await {
        match event_result? {
            Event::NeighborUp(peer_id) => {
                // New connection move to first place
                all_peers.insert(peer_id);
            }
            Event::NeighborDown(peer_id) => {
                // Move to last place
                if all_peers.contains(&peer_id) {
                    all_peers.remove(&peer_id);
                }
                all_peers.insert_end(&peer_id);
            }
            Event::Received(message) => {
                // Move to first place
                if all_peers.contains(&message.delivered_from) {
                    all_peers.remove(&message.delivered_from);
                }
                all_peers.insert(message.delivered_from);
            }
            Event::Lagged => {
            }
        }
    }
```

---

## 3. Terminology & Notation

- **Steward**: one of 5 elected nodes protecting the first valid record each minute  
- **Election Basis Hash**: `previous_record_hash` used in steward election  
- **First Valid Record**: earliest valid write in a minute, that the stewards protected
- **Prev Minute Record**: `unix_minute = floor(now/60) - 1`  


---

## 4. Data Structures

```rust
enum WRITE_REASONS {
    ELECTION=0u8,
    RECOVERY=1u8,
    BOOTSTRAP=2u8,
}

struct TopicRecord {
    // record content 
    topic: [u8; 32],                // sha512( topic_string )[..32]
    unix_minute: u64,               // floor(unixtime / 60)
    election_record_hash: [u8; 32], // 0x0 if first record or no valid last record
    node_ids: [[u8; 32]; 20]>,      // 20 slots
    steward_public_key: [u8; 32],   // ed25519
    write_reason: WRITE_REASONS,    // "election", "bootstrap"

    // record signature
    // ^ ed25519 signature over above fields ^
    signature: [u8; 64],
    this_record_hash: [u8; 32],     // hash of this record itself
}
```
- **election_record_hash**: hash of last valid record, or zero  
- **node_ids**: snapshot of up to 20 peers from `gossip.neighbors()`  
- **steward_public_key**: author of this record  
- **write_reason**: context for this write (election vs. recovery)  


```
# record size in bytes is: 

+ topic(32) 
+ unix_minute(8) 
+ election_record_hash(32) 
+ 20*32(node_ids) 
+ steward_public_key(32) 
+ write_reason(1) 

+ signature(64) 
+ this_record_hash(32) 

= 841 bytes
```

---

## 5. Bootstrap Procedure

1. **First Node**  
   1. `previous_record_hash = 0x00…00`
   2. No previous record → you become `steward[0]`
   3. Publish record
   
2. **Second node**
   1. if joins at same time as first node, pkarr resolves collision and one becomes `steward[0]`
   2. if the record is valid (limited since last record is 0x0) we accept it and wait for the next minute
   3. then we do elections. the last record only held one node id so the second minute the first node will be `steward[0]`
   4. If less then 5 unique nodes are in last minutes record, we assume a low participation regimen and all nodes behave as stewards[0]

3. **Third node** one min later
   1. retrievs T(0) and T(-1) records.
   2. we use the union of T(0) and T(-1) as our bootstrap nodes to join the topic.
   3. we start participating in the next minute (naturally since we are not in the possible connection pool yet).
   4. 
4. **Collision Bootstrap**  
   - Multiple nodes write simultaneously with `election_record_hash=[]`  
   - pkarr resolves collisions → in `BOOTSTRAP` mode we accept any nodes record as long as we can verify that we can connect to that node (if we are also bootstrapping at the moment, otherwise we are the steward). We now act as a steward to protect this record along all others that may or may not have tried and failed to publish too.

---

## 6. Steward Election

```bash
record_node_ids = prev_minute.record.node_ids
election_scores = []
for node_id in record_node_ids:
    score = int.from_le_bytes(
      hash(topic + node_id + unix_minute(now))
    )
    election_scores.push((node_id, score))

sort_desc(election_scores)
steward_node_ids = election_scores[..5].map(|(id,_)| id)
```

- **No eligible nodes**: If `record_node_ids.len() == 0`, you are in `BOOTSTRAP` mode and you behave as `steward[0]`.  

---

## 7. Steward Behavior (Publish & Protect)
Steward ranking is based on the descending order of the election outcome. highest num = s0, second highest = s1, etc.

### Time Slots

| Steward | First Write | Recovery Checks (every 10 s)    |
|---------|-------------|----------------------------------|
| S[0]    | 0 s         | 10 s, 20 s, 30 s, 40 s, 50 s      |
| S[1]    | 2 s         | 12 s, 22 s, 32 s, 42 s, 52 s      |
| S[2]    | 4 s         | 14 s, 24 s, 34 s, 44 s, 54 s      |
| S[3]    | 6 s         | 16 s, 26 s, 36 s, 46 s, 56 s      |
| S[4]    | 8 s         | 18 s, 28 s, 38 s, 48 s, 58 s      |

1. **S[0]** writes at minute start. Others wait their offset.  
2. **All stewards** validate the *first valid* record if they can find it already.
   1. if the record is found, it is saved as *first valid* record and used to overwrite any other non valid records that might have been put their by mistake or maliciously.
   2. if no record is found after the second check of the minute, the steward writes their own valid record in the empty or invalid slot. The next steward finds it and takes it in as the first valid record and overwrites any other invalid records with this one now. 
3. **After 25s** if no valid record found, *all* stewards write their own record ignoring collisions. They then normaly continue their regime, taking the last surviving, valid record after pkarr collisions from multie write as the new *first valid* record.
4. **After 30-60s** non stewards are now resolving the current minutes record to perform elections with. If at this time they can't find any valid record they enter `BOOTSTRAP` mode and behave like steward[0] with lessened validation criteria.
---

## 8. Record Validation Rules

A record is **valid** if and only if:

- `unix_minute == record.unix_minute`  
- `signature` matches `steward_public_key`  
- `election_record_hash` equals last valid record’s hash (or zero but only in `BOOTSTRAP` mode)  
- publisher_node_id is present in `node_ids`
- `this_record_hash` recomputes correctly  

**Else**: treat as *no record*  

---

## 9. Failure & Recovery

- **No record by 25 s** → all stewards write recovery records  
- **No record by 60+ s** → enter `BOOTSTRAP` mode
- **Missing steward** detected in next election automatically excludes them  
